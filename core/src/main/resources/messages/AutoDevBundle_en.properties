name=AutoDev

# chat panel
autodev.chat=Chat

chat.panel.send=Send
chat.panel.newSketch=New Sketch
chat.panel.initial.text='Enter' to start, 'Shift+Enter' for a new line, '/' for call DevIns command, '@' for Agent
chat.too.long.user.message=Message has is {0} tokens too looooooooooooooooooong
chat.input.tips=Content cannot be blank

intention.category.llm=AutoDev
intentions.write.action=Generate code
intentions.assistant.name=AutoDev AI AutoAction
intentions.assistant.popup.title=AutoDev AI Assistant

intentions.chat.code.test.step.prepare-context=Prepare context
intentions.chat.code.test.step.collect-context=Collect prompt context
intentions.chat.code.complete.name=Code complete
intentions.chat.code.complete.family.name=Code complete
intentions.chat.code.test.name=Test this
intentions.chat.code.test.family.name=Test this
intentions.chat.new.family.name=New Chat with code

intentions.chat.selected.code.name=Chat with this code
intentions.chat.selected.fragment.name=Chat with {0} fragment
intentions.chat.selected.element.name=Chat with ''{0}'' {1}
intentions.living.documentation.name=Generate documentation
intentions.living.documentation.family.name=Generate documentation
intentions.request.background.process.title=Your LLM is processing your request

autodev.custom.prompt.placeholder=Custom your prompt here
autodev.custom.intentions.family=Custom Intention

label.submit.issue=<a href="https://github.com/unit-mesh/auto-dev/issues">Want new feature?</a>

# don't remove the following line and don't rename them unless change [LLMSettingCompoent] class
settings.languageParam=Language
settings.gitTypeParam=Git Type
settings.gitLabUrlParam=URL of Gitlab Server
settings.gitLabTokenParam=Gitlab token
settings.gitHubTokenParam=GitHub token
settings.maxTokenLengthParam=Max token length
settings.customEngineServerParam=LLM Server Address
settings.customModelParam=Model Name
settings.customEngineTokenParam=LLM API Key/Token
settings.delaySecondsParam=Quest Delay Seconds
settings.customEngineResponseFormatParam=Custom Response Format (Json Path)
settings.customEngineResponseTypeParam=Custom Response Type
settings.customEngineRequestBodyFormatParam=Custom Request Body Format (Json)
settings.customEngineRequestHeaderFormatParam=Custom Request Header Format (Json)

settings.customize.title=Agentic Customize
counit.agent.enable.label=Enable custom agent (experimental)
counit.agent.json.placeholder=Custom Agent JSON Config


action.new.genius.cicd.github=Generate GitHub Actions

settings.external.team.prompts.path=Team Prompts Path

settings.autodev.coder=Advanced Coding
settings.autodev.coder.trimCodeBeforeSend=Trim Code Before Sending
settings.autodev.coder.recordingInLocal=Recording Instruction In Local
settings.autodev.coder.disableAdvanceContext=Disable Advance Context
settings.autodev.coder.disableAdvanceContext.tips=like framework context, language context, etc.
settings.autodev.coder.inEditorCompletion=Completion in Editor
settings.autodev.coder.noChatHistory=No Chat History
settings.autodev.coder.enableExportAsMcpServer=Enable Export as MCP Server
settings.autodev.coder.enableObserver=Enable Observer Agent
settings.autodev.coder.customActions=Custom prompt Action (Json):

settings.autodev.devops=DevOps (SDLC)

# status bar
autodev.statusbar.name=AutoDev Status
autodev.statusbar.toolTipText=AutoDev
autodev.statusbar.popup.title=AutoDevStatus
autodev.statusbar.id=autodev.statusBarPopup

#PL/SQL
migration.database.plsql=PL/SQL Migration
migration.database.plsql.generate.function=Generate Function
migration.database.plsql.generate.unittest=Generate Unit Test
migration.database.plsql.generate.entity=Generate Entity

# Auto SQL
autosql.name=AutoSQL
autosql.generate=Generate SQL
autosql.generate.clarify=Clarify Requirements
autosql.generate.generate=Generate SQL

# Auto Page
autopage.generate=Frontend Generate
autopage.generate.name=AutoPage Generate
autopage.generate.clarify=Clarify Requirements
autopage.generate.design=Design Page

# Inlay
intentions.chat.inlay.complete.name = Inlay Complete Code
intentions.chat.inlay.complete.family.name = Inlay Complete code
progress.run.task=Running task


# Right click
settings.autodev.rightClick.explain=Explain this
settings.autodev.rightClick.refactor=Refactor this
settings.autodev.rightClick.fixThis=Fix this
settings.autodev.rightClick.chat=Chat with this
settings.autodev.rightClick.genApiTest=Generate API Test


# Others
settings.autodev.others.fixThis=Fix This (AutoDev)
settings.autodev.others.quickAssistant=Quick Assistant
settings.autodev.others.commitMessage=Commit Message (AutoDev)
settings.autodev.others.generateReleaseNote=Generate Release Note (AutoDev)
settings.autodev.others.codeReview=CodeReview (AutoDev)
settings.autodev.others.codeComplete=Code Complete (AutoDev)
settings.autodev.others.editSettings=Edit Settings

# simple prompts
prompts.autodev.explainCode=Explain {0} code
prompts.autodev.refactorCode=Refactor the given {0} code
prompts.autodev.completeCode= complete {0} code, return rest code, no explaining
prompts.autodev.generateTest= Generate test for {0} code
prompts.autodev.fixProblem= Help me fix problem:
prompts.autodev.generateReleaseNote= generate release note
prompts.autodev.generateTestData=Generate API test request (with `http request` code block) based on given {0} code and request/response info. So that we can use it to test for APIs.
settings.autodev.coder.enableRenameSuggestion=Enable Rename suggestion
settings.autodev.coder.enableAutoRepairDiff=Enable auto repair diff
settings.autodev.coder.enableAutoRunTerminal=Enable auto run terminal
settings.autodev.coder.enableAutoLintCode=Enable auto lint patch code
settings.autodev.coder.enableRenderWebview=Enable render chat in WebView
settings.autodev.coder.enableAutoScrollInSketch=Enable AutoScroll in Sketch
settings.autodev.coder.enableDiffViewer=Enable Diff Viewer in Sketch
shell.command.suggestion.action.default.text=How to check out a branch?
batch.nothing.to.testing=Nothing to AutoTest
intentions.chat.code.test.verify=Verify test
custom.agent.open.documents=Open Documents
settings.autodev.coder.testConnectionButton.tips=Don't forget to APPLY change before test connection!

sketch.patch.action.accept=Accept
sketch.patch.action.accept.tooltip=Accept the change
sketch.patch.action.reject=Reject
sketch.patch.action.reject.tooltip=Reject the change
sketch.patch.action.viewDiff.tooltip=View the diff
sketch.patch.action.applyDiff.tooltip=Apply Diff
sketch.patch.action.repairDiff.tooltip=Repair Diff (use FastApply)
# rollback
prompts.autodev.inlineChat=According user selection to ask user question
prompts.autodev.sketch=Sketch
sketch.composer.mode=Composer Mode
sketch.lint.error.tooltip=Click to view all errors
sketch.lint.error=Found Lint issue: {0}
custom.action=Custom Action
custom.living.documentation=Custom Living Documentation
sketch.dependencies.check=Check dependencies has Issues
prompts.autodev.bridge=Bridge
autodev.custom.llms.placeholder=Custom different LLM
settings.autodev.coder.customLlms=Config LLMs: Plan/ACT/Completion/Embedding/FastApply
sketch.compile.devins=Collect context (aka. transpile AutoDev DevIns)
autodev.run.action=Run this file
llm.error.url.scheme=Please, Please, Please configure your model, refer to the document: https://ide.unitmesh.cc/quick-start
counit.mcp.services.placeholder=Config MCP Servers
sketch.plan.finish.task=Please help me finish task:
sketch.plan.review=AI re-evaluate Sketch plan
sketch.plan.edit=Edit Plan
sketch.plan.reviewing=Reviewing Plan
sketch.write.to.file=Write to file
sketch.plan.empty=Last plan is empty
settings.autodev.coder.requires.restart=Require restart
sketch.terminal.execute=Execute Shell
sketch.terminal.copy.text=Copy text
sketch.terminal.send.chat=Send to chat
sketch.terminal.popup=Popup Terminal
autodev.insert.action=Insert to Editor
autodev.copy.action=Copy
autodev.save.action=Save File
sketch.patch.repaired=Repaired
sketch.patch.repair=Repair
sketch.patch.apply=Apply
sketch.patch.view=View
sketch.diff.original=Original
sketch.diff.aiSuggestion=AI suggestion
sketch.patch.failed.read=Failed to read file: {0}
sketch.patch.failed.apply=Failed to apply patch: {0}
sketch.patch.document.null=Document is null for file: {0}
autodev.save.as.file=Save File As
autodev.save.as.file.description=Choose location to save the file
sketch.patch.regenerate=Regenerate
sketch.patch.action.regenerate.tooltip=Regenerate the patch
sketch.terminal.show.hide=Show or hide the terminal

chat.panel.stop=Stop
chat.panel.enhance=Enhance Prompt for AI
chat.panel.clear.all=Clear all
chat.panel.clear.all.tooltip=Clear all files
chat.panel.select.files=Double-click to select related-file to insert into input box
sketch.plan.create=Create issue
planner.stats.changes.empty=No Changes
planner.change.list.title=Change list
planner.action.discard.all=Discard all
planner.action.accept.all=Accept all
planner.stats.no.changes= No changes
planner.no.code.changes=No code changes
planner.stats.changes.count= (Total {0} files changed)
planner.action.view.changes=View changes
planner.action.discard.changes=Discard changes
planner.action.accept.changes=Accept
planner.error.no.file=No {0} file
planner.error.no.after.file=No after file
planner.error.create.file=Create file error {0}
sketch.plan.rerun.task=Help me fix this failed task:
sketch.terminal.stop=Stop Terminal
sketch.mcp.testMcp=Test MCP Services
sketch.mcp.loading=Loading MCP services...
sketch.mcp.services.docs=MCP Documentation
sketch.issue.input.placeholder=Enter Issue Description
sketch.issue.input.submit=Submit
sketch.issue.input.cancel=Cancel
planner.task.status.completed=Completed
planner.task.status.failed=Failed
planner.task.status.in_progress=In Progress
planner.task.status.todo=To Do
planner.task.execute=Execute
chat.panel.add.openFiles=Add all open files
chat.panel.workspace.files=Workspace File
chat.panel.add.files.tooltip=Add files to workspace
chat.panel.select.files.title=Select Files for Workspace
chat.panel.select.files.description=Choose files to add to your workspace
chat.panel.remove.file.tooltip=Remove file from workspace

indexer.generate.domain=Generate domain.csv

# MCP Chat Config Dialog
mcp.chat.config.dialog.title=Model Configuration
mcp.chat.config.dialog.temperature=Temperature: {0}
mcp.chat.config.dialog.enabled.tools=Enabled Tools

# MCP Chat Result Panel
mcp.chat.result.tab.response=Response
mcp.chat.result.tab.tools=Tools
mcp.chat.result.no.tools=No tool calls found in the response
mcp.chat.result.execute=Execute
mcp.chat.result.executing=Executing tool {0}...
mcp.chat.result.error.tool.not.found=Error: Could not find matching tool ''{0}''
mcp.chat.result.execution.time=Duration
mcp.chat.result.execute.all=Execute All Tools
mcp.chat.result.tab.messages=Message Log
# MCP Tool Detail Dialog
mcp.tool.detail.dialog.title=MCP Tool Detail - {0}
mcp.tool.detail.dialog.from.server=From server: {0}
mcp.tool.detail.dialog.no.description=No description available
mcp.tool.detail.dialog.parameters=Parameters
mcp.tool.detail.dialog.verify=Verify (Auto Generate)
mcp.tool.detail.dialog.result=Result
mcp.tool.detail.dialog.execute=Execute
# McpPreviewEditor
mcp.preview.editor.title=MCP Tools
mcp.preview.editor.search.placeholder=Search tools...
mcp.preview.editor.model.label=Model
mcp.preview.editor.configure.button=Configure
mcp.preview.editor.test.button.tooltip=Test Called tools
mcp.preview.editor.empty.message.warning=Please enter a message to send.
mcp.preview.editor.loading.response=Loading response...
# McpFileEditorWithPreview
mcp.editor.preview.title=Preview
mcp.editor.preview.tooltip=Preview panel
mcp.editor.refresh.title=Refresh
shire.toolchain.function.not.found=Toolchain Not Found {0}
shire.run.local.mode=Run Local Model
devins.llm.notfound=LLM not found
devins.llm.done=Done
intentions.step.prepare-context=Prepare Context
devins.intention=Shire intention action
devins.newFile=New File
devins.file=DevIns File
action.view.history.text=View History
action.view.history.description=View conversation history
popup.title.session.history=History
