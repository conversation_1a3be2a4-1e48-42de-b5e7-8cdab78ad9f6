# Existent IDE versions can be found in the following repos:
# https://www.jetbrains.com/intellij-repository/releases/
# https://www.jetbrains.com/intellij-repository/snapshots/
# IC version has Javadoc / Kotlin docs, but no bundle for plugins, JavaScript, etc.
ideaVersion=IU-2024.1

# please see https://www.jetbrains.org/intellij/sdk/docs/basics/getting_started/build_number_ranges.html for description
pluginSinceBuild=241
pluginUntilBuild=252.*

platformPlugins=PlantUML integration:7.10.1-IJ2023.2,com.intellij.mermaid:0.0.24+IJ.243,intellij.jupyter:241.14494.240
#https://plugins.jetbrains.com/plugin/22814-jupyter/versions
#intellij.jupyter:241.14494.240
mermaidPlugin=com.intellij.mermaid:0.0.22+IJ.232
plantUmlPlugin=PlantUML integration:7.10.1-IJ2023.2
# check latest available version here https://plugins.jetbrains.com/plugin/22407-rust/versions
rustPlugin=com.jetbrains.rust:241.25026.24

# https://plugins.jetbrains.com/plugin/9568-go/versions
goPlugin=org.jetbrains.plugins.go:241.14494.240

pythonPlugin=PythonCore:241.14494.240
jupyterPlugin=intellij.jupyter:241.14494.240
devContainerPlugin=org.jetbrains.plugins.docker.gateway:241.14494.189

endpointsPlugin=com.intellij.microservices.ui:241.14494.150
swaggerPlugin=com.intellij.swagger:241.14494.150
vuePlugin=org.jetbrains.plugins.vue:241.14494.159

openWritePlugin=com.intellij.openRewrite:241.14494.158